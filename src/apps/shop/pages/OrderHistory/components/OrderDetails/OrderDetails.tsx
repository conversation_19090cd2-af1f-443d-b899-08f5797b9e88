import { But<PERSON>, Divider, Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';

import { useOrderDetails } from '../../services/useOrderDetails';
import { getPriceString } from '@/utils';
import styles from './OrderDetails.module.css';
import { FEATURE_FLAGS } from '@/constants';
import { OrderVendorPanel } from '../OrderVendorPanel/OrderVendorPanel';
import { ContentLoader } from '@/libs/ui/ContentLoader/ContentLoader';
import { DownloadInvoicesLink } from '@/libs/orders/components/DownloadInvoicesLink/DownloadInvoicesLink';
import { DownloadChecklist } from '@/libs/orders/components/DownloadChecklist/DownloadChecklist';
import { VendorOrderSummary } from './VendorOrderSummary';
import { getVendorPromotions } from '@/libs/orders/utils/promotionGrouping';

interface OrderDetailsProps {
  id: string;
}
export const OrderDetails = ({ id }: OrderDetailsProps) => {
  const { order, isLoading } = useOrderDetails({ id });
  const {
    orderNumber,
    totalPrice,
    downloadInvoicesUrl,
    downloadChecklistUrl,
    vendorOrders = [],
    promotions = [],
  } = order ?? {};

  if (isLoading || !order) {
    return <ContentLoader />;
  }

  return (
    <Flex p="1.5rem" direction="column" className={styles.container}>
      <Flex gap="xl">
        <div>
          <div className="pb-4">
            <Text c="#666" size="xs" mb="xs">
              Order ID
            </Text>
            <Text c="#333" size="xlLg" fw="bold" inline>
              {orderNumber}
            </Text>
          </div>
          <Divider mb="md" />
          <div className="pb-4">
            <Text c="#666" size="xs">
              Order Total
            </Text>
            <Text c="#333" size="xlLg" fw="bold">
              {getPriceString(totalPrice)}
            </Text>
          </div>
          <div className="pb-4">
            <Divider mb="md" />
            <Flex direction="column">
              {downloadInvoicesUrl && (
                <DownloadInvoicesLink url={downloadInvoicesUrl} />
              )}
              {downloadChecklistUrl && (
                <DownloadChecklist url={downloadChecklistUrl} />
              )}
            </Flex>
          </div>
          {FEATURE_FLAGS.ORDER_STORY_COMPLETE && (
            <>
              <Flex direction="column">
                <Button>Repeat Order</Button>
              </Flex>
            </>
          )}
        </div>
        <div className="flex-grow">
          <Text c="#666" size="xs" mb="xs">
            Order Details
          </Text>
          <div className={`bg-[#F8FBFD] p-6 ${styles.info}`}>
            {vendorOrders.map(
              (
                { items, vendor, totalTaxFee, shippingFee, totalPrice },
                index,
              ) => (
                <VendorOrderSummary
                  key={vendor.id}
                  vendor={vendor}
                  items={items}
                  totalTaxFee={totalTaxFee}
                  shippingFee={shippingFee}
                  totalPrice={totalPrice}
                  showDivider={index !== 0}
                />
              ),
            )}
          </div>
        </div>
      </Flex>
      <Divider my="xl" />
      {vendorOrders.map(({ vendor, items, totalPrice }) => (
        <div key={vendor.id} className="mb-4">
          <OrderVendorPanel
            totalPrice={+totalPrice}
            items={items}
            vendor={vendor}
            promotions={getVendorPromotions({ id: vendor.id, promotions })}
          />
        </div>
      ))}
    </Flex>
  );
};
