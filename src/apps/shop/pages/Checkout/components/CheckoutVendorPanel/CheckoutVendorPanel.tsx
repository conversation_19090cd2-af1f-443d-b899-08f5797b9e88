import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { Image } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import defaultProductImgUrl from '@/assets/images/default-product.png';
import { usePromotionData } from '@/libs/promotions/hooks/usePromotionData';
import { CheckoutPromoItem } from '../CheckoutPromoItem/CheckoutPromoItem';
import { CheckoutItem } from '../CheckoutItem/CheckoutItem';
import { CartVendorType } from '@/libs/cart/types';

type CheckoutVendorPanelProps = {
  vendor: CartVendorType;
};

export const CheckoutVendorPanel = ({ vendor }: CheckoutVendorPanelProps) => {
  const { id, name, imageUrl, items } = vendor;
  const { promotions, nonPromotionItems } = usePromotionData(items);

  return (
    <div key={id} className="mb-4">
      <CollapsiblePanel
        header={
          <Flex align="center" pr="5rem">
            <Image
              src={imageUrl}
              alt={name}
              fallbackSrc={defaultProductImgUrl}
              h={42}
              title={name}
            />
          </Flex>
        }
        content={
          <div className="grid divide-y divide-gray-200/80 text-sm">
            {nonPromotionItems.map((item) => (
              <CheckoutItem key={item.productOfferId} item={item} />
            ))}
            {promotions.buy_x_get_y && (
              <CheckoutPromoItem promoItem={promotions.buy_x_get_y} />
            )}
          </div>
        }
        startOpen
      />
    </div>
  );
};
