import { VendorType } from '@/types';

export interface GpoVendorData {
  vendor: VendorType;
  logo: {
    color: string;
    text: string;
    subtitle?: string;
  };
  marketShare: {
    percentage: number;
    value: number;
    total: number;
    change: number;
    isPositive: boolean;
  };
  totalSpend: {
    value: number;
    change?: number;
    isPositive?: boolean;
  };
  growthTarget: {
    percentage: number;
    value: number;
    goal: number;
  };
  progress: {
    current: number;
    goal: number;
    prevYear: number;
  };
}

export const mockVendors: GpoVendorData[] = [
  {
    vendor: {
      id: 'mwi',
      name: 'MWI',
      status: 'connected',
      type: 'distributor',
      imageUrl: '',
      alert: null,
      integrationPoints: [],
      authenticationKind: '',
      authenticationConfiguration: null,
      lastProductCatalogSync: null,
    },
    logo: {
      color: '#447BFD',
      text: 'MWI',
      subtitle: 'Animal Health',
    },
    marketShare: {
      percentage: 10,
      value: 5,
      total: 50,
      change: 1,
      isPositive: true,
    },
    totalSpend: {
      value: 5000000,
    },
    growthTarget: {
      percentage: 83,
      value: 5,
      goal: 6,
    },
    progress: {
      current: 5000000,
      goal: 6000000,
      prevYear: 4500000,
    },
  },
  {
    vendor: {
      id: 'zoetis',
      name: 'Zoetis',
      status: 'connected',
      type: 'manufacturer',
      imageUrl: '',
      alert: null,
      integrationPoints: [],
      authenticationKind: '',
      authenticationConfiguration: null,
      lastProductCatalogSync: null,
    },
    logo: {
      color: '#ED7F02',
      text: 'zoetis',
    },
    marketShare: {
      percentage: 10,
      value: 5,
      total: 50,
      change: 3,
      isPositive: false,
    },
    totalSpend: {
      value: 5000000,
      change: 6,
      isPositive: false,
    },
    growthTarget: {
      percentage: 80,
      value: 5,
      goal: 6,
    },
    progress: {
      current: 5000000,
      goal: 6000000,
      prevYear: 4800000,
    },
  },
  {
    vendor: {
      id: 'covetrus',
      name: 'Covetrus',
      status: 'connected',
      type: 'distributor',
      imageUrl: '',
      alert: null,
      integrationPoints: [],
      authenticationKind: '',
      authenticationConfiguration: null,
      lastProductCatalogSync: null,
    },
    logo: {
      color: '#6B46C1',
      text: 'covetrus',
    },
    marketShare: {
      percentage: 10,
      value: 5.7,
      total: 50,
      change: 0,
      isPositive: true,
    },
    totalSpend: {
      value: 5700000,
    },
    growthTarget: {
      percentage: 95,
      value: 5.7,
      goal: 6,
    },
    progress: {
      current: 5700000,
      goal: 6000000,
      prevYear: 5200000,
    },
  },
];
