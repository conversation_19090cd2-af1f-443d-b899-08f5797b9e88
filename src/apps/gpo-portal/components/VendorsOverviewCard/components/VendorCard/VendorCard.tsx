import { Text } from '@mantine/core';
import { GpoVendorData } from '../../data/mockedVendor';
import { getPriceString } from '@/utils';
import { Button } from '@/libs/ui/Button/Button';
import { Icon } from '@/libs/icons/Icon';
import { ProgressBar } from '@/libs/dashboard';
import { Badge } from '@/libs/ui/Badge/Badge';

interface VendorCardProps {
  vendorData: GpoVendorData;
}

export const VendorCard = ({ vendorData }: VendorCardProps) => {
  const formatPercentage = (value: number) => {
    return `${value}%`;
  };

  const calculateProgressPercentage = () => {
    return Math.min(
      (vendorData.progress.current / vendorData.progress.goal) * 100,
      100,
    );
  };

  const calculatePrevYearPercentage = () => {
    return Math.min(
      (vendorData.progress.prevYear / vendorData.progress.goal) * 100,
      100,
    );
  };

  const remainingAmount =
    vendorData.progress.goal - vendorData.progress.current;

  return (
    <div className="rounded-xl border border-[#F5F5F5] bg-white p-5">
      <div className="mb-4 flex items-center justify-between">
        <div className="flex w-[268px] items-center gap-3">
          <div className="flex h-12 flex-col items-center justify-center rounded-lg text-center">
            <img
              src="https://demo.services.highfive.vet/storage/vendor-images/N2Q4IRIggTnyZQZMIiz4vQm2i7gjrJbnS0v94uT6.png"
              alt={vendorData.logo.text}
              className="h-full w-auto"
            />
          </div>
          <div className="flex flex-col gap-[0.3rem]">
            <h4 className="text-[1rem] font-medium text-[#344054]">
              {vendorData.vendor.name}
            </h4>
            <p className="text-sm font-bold text-[#666] capitalize">
              {vendorData.vendor.type}
            </p>
          </div>
        </div>

        <div className="divider-v h-8" />

        <div className="flex flex-1 items-center justify-between">
          <div className="flex min-w-[190px] flex-col px-8">
            <Text size="xs" c="#98A2B3" mb="4px">
              Market Share %
            </Text>
            <div className="flex items-center gap-2">
              <Text size="sm" fw={600} c="#344054">
                {formatPercentage(vendorData.marketShare.percentage)} (
                {getPriceString(vendorData.marketShare.value)} from{' '}
                {getPriceString(vendorData.marketShare.total)})
              </Text>
            </div>
          </div>
          <div className="divider-v h-8" />
          <div className="flex min-w-[190px] flex-col px-8">
            <Text size="xs" c="#98A2B3" mb="4px">
              Total Spend
            </Text>
            <div className="flex items-center gap-2">
              <Text size="sm" fw={600} c="#344054">
                {getPriceString(vendorData.totalSpend.value)}
              </Text>
            </div>
          </div>
          <div className="divider-v h-8" />
          <div className="flex min-w-[190px] flex-col px-8">
            <Text size="xs" c="#98A2B3" mb="4px">
              Growth Target %
            </Text>
            <div className="flex items-center gap-2">
              <Text size="sm" fw={600} c="#344054">
                {formatPercentage(vendorData.growthTarget.percentage)} (
                {getPriceString(vendorData.growthTarget.value)} from{' '}
                {getPriceString(vendorData.growthTarget.goal)})
              </Text>
            </div>
          </div>
        </div>

        <div className="flex w-[80px] items-center gap-2">
          <Button
            variant="white"
            className="max-w-[60px]"
            aria-label="Download"
            onClick={() => {
              console.log('WIP...');
            }}
          >
            <Icon name="download" aria-hidden={true} />
          </Button>
          <Button
            variant="unstyled"
            className="max-w-[60px]"
            aria-label="More options"
            onClick={() => {
              console.log('WIP...');
            }}
          >
            <Icon name="moreOptions" aria-hidden={true} />
          </Button>
        </div>
      </div>

      <div className="divider-h w-full" />

      <div className="mt-4 flex items-center rounded-full bg-[#F2F2F2] py-[14px]">
        <div className="flex w-[268px] items-center justify-between pl-4">
          <p className="text-sm font-bold text-[#333]">
            {getPriceString(remainingAmount)}{' '}
            <span className="text-xs font-normal text-[#98A2B3]">
              until your goal
            </span>
          </p>
        </div>
        <div className="divider-v h-8" />
        <div className="flex flex-1 items-center justify-between pr-4">
          <Badge className="h-[20px] w-[68px] bg-[#518ef8] p-0 text-xs font-bold text-white">
            Start
          </Badge>
          <div className="flex-1 px-2 pt-4">
            <ProgressBar
              showLegend={false}
              values={[
                {
                  value: calculateProgressPercentage(),
                  color: '#518ef8',
                },
                {
                  value: calculatePrevYearPercentage(),
                  color: '#98A2B3',
                },
              ]}
            />
          </div>
          <Badge className="h-[20px] w-[68px] bg-[rgba(82,82,82,0.4)] p-0 text-xs font-bold text-white">
            Goal
          </Badge>
        </div>
      </div>
    </div>
  );
};
