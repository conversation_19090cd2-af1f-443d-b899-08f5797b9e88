import { VendorCard } from './components/VendorCard/VendorCard';
import { mockVendors } from './data/mockedVendor';
import { useTimePeriod } from '@/libs/utils/hooks/useTimePeriod/useTimePeriod';
import { Select } from '@/libs/form/Select';
import type { PeriodOption } from '@/libs/utils/hooks/useTimePeriod/useTimePeriod';
import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { Button } from '@/libs/ui/Button/Button';
import { Icon } from '@/libs/icons/Icon';

export const VendorsOverviewCard = () => {
  const {
    options: timePeriodsOptions,
    period: selectedPeriod,
    setPeriod,
    formattedRange,
  } = useTimePeriod({
    defaultPeriod: 'entire year',
    availableOptions: [
      'entire year',
      'Q1',
      'Q2',
      'Q3',
      'Q4',
      'last month',
      'last quarter',
    ],
  });

  const timePeriods = timePeriodsOptions.map((option) => ({
    value: option,
    label:
      option.charAt(0).toUpperCase() +
      option.slice(1).replace(/([A-Z])/g, ' $1'),
  }));

  return (
    <CollapsiblePanel
      startOpen
      header={
        <div className="flex w-full items-center justify-between py-3 pr-18 pl-6">
          <h3 className="text-base font-medium text-[#344054]">
            Vendors Overview
          </h3>

          <div className="w-[160px]">
            <Select
              value={selectedPeriod}
              onChange={(e) => setPeriod(e.target.value as PeriodOption)}
              options={timePeriods}
            />
          </div>
        </div>
      }
      content={
        <div className="bg-white p-6">
          <div className="flex flex-col gap-4 rounded bg-[#FAFAFA] p-6">
            <div className="flex items-center justify-between gap-2">
              <p className="text-sm text-[#666]">
                Results filtered by:{' '}
                <span className="font-bold text-[#333] capitalize">
                  {selectedPeriod} ({formattedRange})
                </span>
              </p>
              <div className="flex items-center gap-2">
                <Button
                  variant="white"
                  className="max-w-[60px]"
                  aria-label="Download"
                  onClick={() => {
                    console.log('WIP...');
                  }}
                >
                  <Icon name="download" aria-hidden={true} />
                </Button>
                <Button
                  variant="unstyled"
                  className="max-w-[60px]"
                  aria-label="More options"
                  onClick={() => {
                    console.log('WIP...');
                  }}
                >
                  <Icon name="moreOptions" aria-hidden={true} />
                </Button>
              </div>
            </div>

            {mockVendors.map((vendorData) => (
              <VendorCard key={vendorData.vendor.id} vendorData={vendorData} />
            ))}
          </div>
        </div>
      }
    />
  );
};
