import { useMemo } from 'react';
import { CartItemType } from '@/libs/cart/types';
import {
  getBuyXGetYPromotionDetails,
  groupTriggeredPromotionItems,
  hasTriggeredPromotion,
} from '../utils/promotionUtils';
import { BuyXGetYPromotionData, ProcessedPromotionData } from '../types';

type PromotionType = Record<string, { items: CartItemType[] }> & {
  buy_x_get_y?: BuyXGetYPromotionData;
};

export function usePromotionData(
  items: CartItemType[],
): ProcessedPromotionData {
  return useMemo(() => {
    let promotions: PromotionType = groupTriggeredPromotionItems(items);

    if (promotions.buy_x_get_y) {
      promotions = {
        ...promotions,
        buy_x_get_y: {
          ...getBuyXGetYPromotionDetails(promotions.buy_x_get_y.items),
        },
      };
    }

    const nonPromotionItems = items.filter(
      (item) => !hasTriggeredPromotion(item),
    );

    return {
      promotions,
      nonPromotionItems,
    };
  }, [items]);
}
