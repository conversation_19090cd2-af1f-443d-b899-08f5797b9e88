/**
 * Core promotion utilities containing shared business logic
 * for both cart promotions and order history promotions
 */

// Normalized interfaces for cross-context promotion handling
export interface NormalizedPromotionRule {
  minimumQuantity: number;
  freeQuantityPerTrigger: number;
}

export interface NormalizedPromotionItem {
  quantity: number;
  unitPrice: number;
  subtotal: number;
}

export interface PromotionCalculationResult {
  subtotalPaidItems: number;
  subtotalAllItems: number;
  paidItemsQty: number;
  freeItemsQty: number;
}

/**
 * Calculate how many times a promotion is triggered based on quantity and minimum requirement
 */
export const calculatePromotionTriggers = (
  quantity: number,
  minimumQuantity: number,
): number => {
  return Math.floor(quantity / minimumQuantity);
};

/**
 * Calculate total free items based on number of triggers and free quantity per trigger
 */
export const calculateFreeItems = (
  triggers: number,
  freeQtyPerTrigger: number,
): number => {
  return triggers * freeQtyPerTrigger;
};

/**
 * Calculate promotion subtotals for paid and all items
 */
export const calculatePromotionSubtotals = ({
  paidQuantity,
  freeQuantity,
  unitPrice,
  paidSubtotal,
}: {
  paidQuantity: number;
  freeQuantity: number;
  unitPrice: number;
  paidSubtotal: number;
}): PromotionCalculationResult => {
  return {
    subtotalPaidItems: paidSubtotal,
    subtotalAllItems: (paidQuantity + freeQuantity) * unitPrice,
    paidItemsQty: paidQuantity,
    freeItemsQty: freeQuantity,
  };
};

/**
 * Complete promotion calculation combining triggers, free items, and subtotals
 */
export const calculatePromotionDetails = ({
  quantity,
  unitPrice,
  paidSubtotal,
  rule,
}: {
  quantity: number;
  unitPrice: number;
  paidSubtotal: number;
  rule: NormalizedPromotionRule;
}): PromotionCalculationResult & { triggers: number } => {
  const triggers = calculatePromotionTriggers(quantity, rule.minimumQuantity);
  const freeItemsQty = calculateFreeItems(triggers, rule.freeQuantityPerTrigger);
  
  const subtotals = calculatePromotionSubtotals({
    paidQuantity: quantity,
    freeQuantity: freeItemsQty,
    unitPrice,
    paidSubtotal,
  });

  return {
    ...subtotals,
    triggers,
  };
};

/**
 * Aggregate multiple promotion calculation results
 */
export const aggregatePromotionResults = (
  results: PromotionCalculationResult[],
): PromotionCalculationResult => {
  return results.reduce(
    (acc, result) => ({
      subtotalPaidItems: acc.subtotalPaidItems + result.subtotalPaidItems,
      subtotalAllItems: acc.subtotalAllItems + result.subtotalAllItems,
      paidItemsQty: acc.paidItemsQty + result.paidItemsQty,
      freeItemsQty: acc.freeItemsQty + result.freeItemsQty,
    }),
    {
      subtotalPaidItems: 0,
      subtotalAllItems: 0,
      paidItemsQty: 0,
      freeItemsQty: 0,
    },
  );
};
